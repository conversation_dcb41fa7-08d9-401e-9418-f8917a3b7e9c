using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Rules;
using TikTok.Domain.Entities.Rules;
using TikTok.Entities;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Application.Rules;

public class RuleAdAccountAppService : CrudAppService<RuleAdAccountEntity, RuleAdAccountDto, Guid>, IRuleAdAccountAppService
{
    private readonly IRepository<RawAdAccountEntity, Guid> _rawAdAccountRepository;
    private readonly IRepository<RawBusinessCenterEntity, Guid> _rawBusinessCenterRepository;

    public RuleAdAccountAppService(IRepository<RuleAdAccountEntity, Guid> repository, IRepository<RawAdAccountEntity, Guid> rawAdAccountRepository, IRepository<RawBusinessCenterEntity, Guid> rawBusinessCenterRepository) : base(repository)
    {
        _rawAdAccountRepository = rawAdAccountRepository;
        _rawBusinessCenterRepository = rawBusinessCenterRepository;
    }

    public async Task<List<RuleAdAccountDto>> AddAdAccountsToRule(AddAdAccountsToRuleDto input)
    {
        var ruleAdAccounts = await Repository.GetListAsync(x => x.RuleId == input.RuleId);

        var dataOlds = await Repository.GetListAsync(x => x.RuleId == input.RuleId && x.CreatorId == CurrentUser.Id && x.AdAccountId.HasValue && input.AdAccountIds.Contains(x.AdAccountId.Value));
        var ruleAdAccountInserts = new List<RuleAdAccountEntity>();
        foreach (var adAccountId in input.AdAccountIds)
        {
            var dataOld = dataOlds.FirstOrDefault(x => x.AdAccountId == adAccountId);
            if (dataOld == null)
            {
                var ruleAdAccount = new RuleAdAccountEntity(Guid.NewGuid())
                {
                    RuleId = input.RuleId,
                    BcId = null,
                    AdAccountId = adAccountId
                };
                ruleAdAccountInserts.Add(ruleAdAccount);
            }
        }

        if (ruleAdAccountInserts.Any())
        {
            await Repository.InsertManyAsync(ruleAdAccountInserts);
        }

        return ObjectMapper.Map<List<RuleAdAccountEntity>, List<RuleAdAccountDto>>(ruleAdAccountInserts);
    }

    public async Task<List<RuleAdAccountDto>> GetRuleAdAccountAsync(Guid? bcId, Guid? adAccountId)
    {
        var result = await Repository.GetListAsync(x =>
            x.BcId == bcId && x.AdAccountId == adAccountId && x.CreatorId == CurrentUser.Id);
        return ObjectMapper.Map<List<RuleAdAccountEntity>, List<RuleAdAccountDto>>(result);
    }

    public async Task UpdateRuleAdAccountAsync(Guid? bcId, Guid? adAccountId, List<Guid> rules)
    {
        // Validate input parameters
        if (rules == null)
        {
            rules = new List<Guid>();
        }

        // Get existing rule ad account relationships for the given bcId and adAccountId
        var existingRuleAdAccounts = await Repository.GetListAsync(x =>
            x.BcId == bcId && x.AdAccountId == adAccountId && x.CreatorId == CurrentUser.Id);

        // Get existing rule IDs
        var existingRuleIds = existingRuleAdAccounts.Select(x => x.RuleId).ToList();

        // Find rules to delete (existing rules that are not in the new rules list)
        var rulesToDelete = existingRuleAdAccounts.Where(x => !rules.Contains(x.RuleId)).ToList();

        // Delete rules that are no longer needed
        foreach (var ruleToDelete in rulesToDelete)
        {
            await Repository.DeleteAsync(ruleToDelete);
        }

        // Find rules to create (new rules that don't exist yet)
        var rulesToCreate = rules.Where(ruleId => !existingRuleIds.Contains(ruleId)).ToList();

        // Create new rule ad account relationships
        foreach (var ruleId in rulesToCreate)
        {
            var newRuleAdAccount = new RuleAdAccountEntity
            {
                RuleId = ruleId,
                BcId = bcId,
                AdAccountId = adAccountId
            };

            await Repository.InsertAsync(newRuleAdAccount);
        }
    }

    public async Task DeleteAdAccountsFromRule(Guid ruleId, List<Guid> adAccountIds)
    {
        var ruleAdAccounts = await Repository.GetListAsync(x => x.RuleId == ruleId && adAccountIds.Contains(x.AdAccountId.Value) && x.CreatorId == CurrentUser.Id);

        foreach (var ruleAdAccount in ruleAdAccounts)
        {
            await Repository.DeleteAsync(ruleAdAccount);
        }
    }

    public async Task UpdateRuleAdvertiserAsync(string? bcId, string? advertiserId, List<Guid> rules)
    {
        Guid? appBcId = null;
        Guid? adAccountId = null;

        if (!string.IsNullOrEmpty(bcId))
        {
            appBcId = (await _rawBusinessCenterRepository.FirstOrDefaultAsync(x => x.BcId == bcId))?.Id;
        }

        if (!string.IsNullOrEmpty(advertiserId))
        {
            adAccountId = (await _rawAdAccountRepository.FirstOrDefaultAsync(x => x.AdvertiserId == advertiserId))?.Id;
        }

        await UpdateRuleAdAccountAsync(appBcId, adAccountId, rules);
    }
}