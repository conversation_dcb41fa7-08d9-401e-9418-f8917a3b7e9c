﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Domain.Entities.Rules;
using TikTok.Entities;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace TikTok.DbMigrator.Data.UpdateDb
{
    public class UpdateRuleAdAccountDataSeeder : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<RuleAdAccountEntity, Guid> _ruleAdAccountRepository;
        private readonly IRepository<RawAdAccountEntity, Guid> _rawAdAccountRepository;
        public UpdateRuleAdAccountDataSeeder(IRepository<RuleAdAccountEntity, Guid> repository, IRepository<RawAdAccountEntity, Guid> adAccountRepository)
        {
            _ruleAdAccountRepository = repository;
            _rawAdAccountRepository = adAccountRepository;

        }

        public async Task SeedAsync(DataSeedContext context)
        {
            var needUpdateRuleAdAccounts = (await _ruleAdAccountRepository.GetQueryableAsync()).Where(x => x.AdAccountId.HasValue &&( x.AdvertiserId==null ||x.AdvertiserId=="")).ToList();
            if (needUpdateRuleAdAccounts.IsNullOrEmpty())
            {
                return;
            }
            var adAccountIds = needUpdateRuleAdAccounts.Select(x => x.AdAccountId).ToList();
            var adaccounts = (await _rawAdAccountRepository.GetQueryableAsync()).Where(x => adAccountIds.Contains(x.Id)).ToList();
            foreach (var item in needUpdateRuleAdAccounts)
            {
                var adaccount = adaccounts.FirstOrDefault(x => x.Id == item.AdAccountId);
                if (adaccount != null)
                {
                    item.AdvertiserId = adaccount.AdvertiserId;
                }
            }
            await _ruleAdAccountRepository.UpdateManyAsync(needUpdateRuleAdAccounts);
        }
    }
}
