using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Rules;
using TikTok.BusinessApplications;
using TikTok.Domain.Entities.Rules;
using TikTok.Repositories;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Application.Rules
{
    /// <summary>
    /// Service implementation for retrieving rules by business center and advertiser IDs
    /// </summary>
    public class RuleAdAccountService : IRuleAdAccountService
    {
        private readonly IRepository<RuleAdAccountEntity, Guid> _ruleAdAccountRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IBusinessApplicationCache _businessApplicationCache;

        public RuleAdAccountService(
            IRepository<RuleAdAccountEntity, Guid> ruleAdAccountRepository,
            IAdAccountRepository adAccountRepository,
            IBusinessApplicationCache businessApplicationCache)
        {
            _ruleAdAccountRepository = ruleAdAccountRepository;
            _adAccountRepository = adAccountRepository;
            _businessApplicationCache = businessApplicationCache;
        }

        public async Task<List<RuleAdAccountFlatDto>> GetRulesByBcIdAndAdvertiserIdsAsync(string bcId, List<string> advertiserIds)
        {
            if (string.IsNullOrEmpty(bcId))
            {
                return new List<RuleAdAccountFlatDto>();
            }

            var bc = await _businessApplicationCache.GetByBcIdAsync(bcId);
            if (bc == null)
            {
                return new List<RuleAdAccountFlatDto>();
            }

            // 1. Lấy danh sách AdAccount entities theo AdvertiserIds
            List<Guid> adAccountIds = new List<Guid>();
            Dictionary<Guid, string> adAccountIdToAdvertiserIdMap = new Dictionary<Guid, string>();

            if (advertiserIds != null && advertiserIds.Any())
            {
                var adAccounts = await _adAccountRepository.GetListByAdvertiserIdsAsync(advertiserIds);
                adAccountIds = adAccounts.Select(x => x.Id).ToList();
                adAccountIdToAdvertiserIdMap = adAccounts.ToDictionary(x => x.Id, x => x.AdvertiserId);
            }

            // 2. Lấy danh sách RuleAdAccountEntity theo BcId và AdAccountIds
            var ruleAdAccountQuery = await _ruleAdAccountRepository.GetQueryableAsync();

            var ruleAdAccounts = ruleAdAccountQuery
                .Where(x =>
                    // Rule áp dụng cho tất cả AdAccount trong BC (AdAccountId == null)
                    x.AdAccountId == null && x.BcId == bc.Id ||
                    // Rule áp dụng cho AdAccount cụ thể
                    x.AdAccountId.HasValue && adAccountIds.Contains(x.AdAccountId.Value)
                )
                .ToList();

            // 3. Tạo danh sách kết quả dạng phẳng
            var result = new List<RuleAdAccountFlatDto>();

            foreach (var ruleAdAccount in ruleAdAccounts)
            {
                if (ruleAdAccount.AdAccountId == null)
                {
                    // Rule áp dụng cho tất cả AdAccount trong BC
                    // Tạo một record cho mỗi AdvertiserId
                    foreach (var advertiserId in advertiserIds ?? new List<string>())
                    {
                        result.Add(new RuleAdAccountFlatDto
                        {
                            Id = ruleAdAccount.Id,
                            RuleId = ruleAdAccount.RuleId,
                            BcId = bcId,
                            AdvertiserId = advertiserId
                        });
                    }
                }
                else
                {
                    // Rule áp dụng cho AdAccount cụ thể
                    // Sử dụng map để lấy AdvertiserId
                    if (adAccountIdToAdvertiserIdMap.TryGetValue(ruleAdAccount.AdAccountId.Value, out var advertiserId))
                    {
                        result.Add(new RuleAdAccountFlatDto
                        {
                            Id = ruleAdAccount.Id,
                            RuleId = ruleAdAccount.RuleId,
                            BcId = bcId,
                            AdvertiserId = advertiserId,
                            RuleAssignerId = ruleAdAccount.CreatorId
                        });
                    }
                }
            }

            return result;
        }
    }
}
