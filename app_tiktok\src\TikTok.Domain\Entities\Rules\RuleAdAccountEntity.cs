using System;
using TikTok.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Domain.Entities.Rules
{
    /// <summary>
    /// Relationship between Rule and AdAccount
    /// </summary>
    public class RuleAdAccountEntity : AuditedEntity<Guid>
    {
        public RuleAdAccountEntity()
        {
            
        }

        public RuleAdAccountEntity(Guid key) : base(key)
        {
        }

        public Guid RuleId { get; set; }
        public RuleEntity Rule { get; set; }

        /// <summary>
        /// Nếu BcId là null thì Rule áp dụng cho tất cả Bc
        /// </summary>
        public Guid? BcId { get; set; }
        public RawBusinessCenterEntity? Bc { get; set; }

        /// <summary>
        /// Nếu AdAccountId là null thì Rule áp dụng cho tất cả AdAccount trong Bc
        /// </summary>
        public Guid? AdAccountId { get; set; }
        public RawAdAccountEntity? AdAccount { get; set; }
        public string? AdvertiserId { get; set; }
    }
}